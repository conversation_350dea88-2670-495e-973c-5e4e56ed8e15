import { useState } from "react";
import { Download, FileText, Calendar, Car, User, Receipt } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";

interface PDFPreviewModalProps {
  children: React.ReactNode;
  invoices?: any[];
  title?: string;
}

export function PDFPreviewModal({ children, invoices = [], title = "تقرير الفواتير" }: PDFPreviewModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const mockData = {
    companyName: "نظام إدارة السيارات",
    reportDate: new Date().toLocaleDateString('ar-SA'),
    totalInvoices: invoices.length || 4,
    totalAmount: 3000,
    paidAmount: 1650,
    remainingAmount: 1350,
    invoices: invoices.length ? invoices : [
      { number: "INV-2024-001", date: "2024-01-15", service: "تغيير زيت المحرك", total: 300, status: "مدفوع" },
      { number: "INV-2024-002", date: "2024-01-20", service: "إصلاح الفرامل", total: 850, status: "مدفوع جزئياً" },
      { number: "INV-2024-003", date: "2024-02-05", service: "صيانة شاملة", total: 1200, status: "معلق" },
      { number: "INV-2024-004", date: "2024-02-10", service: "تغيير الإطارات", total: 650, status: "متأخر" }
    ]
  };

  const handleDownload = () => {
    toast({
      title: "جاري تحضير التقرير",
      description: "سيتم تحميل ملف PDF خلال لحظات...",
    });
    
    // Simulate PDF generation
    setTimeout(() => {
      toast({
        title: "تم تحميل التقرير",
        description: "تم حفظ التقرير في مجلد التحميلات",
      });
      setIsOpen(false);
    }, 2000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "مدفوع": return "bg-success text-success-foreground";
      case "مدفوع جزئياً": return "bg-warning text-warning-foreground";
      case "معلق": return "bg-muted text-muted-foreground";
      case "متأخر": return "bg-destructive text-destructive-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <FileText className="w-4 h-4 text-primary-foreground" />
            </div>
            معاينة {title}
          </DialogTitle>
        </DialogHeader>

        {/* PDF Preview */}
        <div className="bg-white border-2 border-border rounded-lg p-8 mx-auto" style={{ width: '210mm', minHeight: '297mm' }}>
          {/* Header */}
          <div className="text-center mb-8 border-b-2 border-primary pb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary-dark rounded-full mx-auto mb-4 flex items-center justify-center">
              <Car className="w-8 h-8 text-primary-foreground" />
            </div>
            <h1 className="text-3xl font-almarai font-bold text-primary mb-2">
              {mockData.companyName}
            </h1>
            <h2 className="text-xl font-arabic text-muted-foreground mb-2">{title}</h2>
            <div className="flex justify-center items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span>تاريخ التقرير: {mockData.reportDate}</span>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <Card className="p-4 text-center border-primary/20">
              <div className="text-2xl font-bold text-primary mb-1">{mockData.totalInvoices}</div>
              <div className="text-xs text-muted-foreground">إجمالي الفواتير</div>
            </Card>
            <Card className="p-4 text-center border-success/20">
              <div className="text-2xl font-bold text-success mb-1">{mockData.paidAmount.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">المبلغ المدفوع</div>
            </Card>
            <Card className="p-4 text-center border-warning/20">
              <div className="text-2xl font-bold text-warning mb-1">{mockData.remainingAmount.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">المبلغ المتبقي</div>
            </Card>
            <Card className="p-4 text-center border-foreground/20">
              <div className="text-2xl font-bold text-foreground mb-1">{mockData.totalAmount.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">الإجمالي</div>
            </Card>
          </div>

          {/* Invoice Table */}
          <div className="mb-8">
            <h3 className="text-lg font-arabic font-semibold mb-4 flex items-center gap-2">
              <Receipt className="w-5 h-5" />
              تفاصيل الفواتير
            </h3>
            
            <div className="border border-border rounded-lg overflow-hidden">
              <table className="w-full text-sm">
                <thead className="bg-muted/50">
                  <tr>
                    <th className="text-right p-3 font-arabic font-semibold border-b">رقم الفاتورة</th>
                    <th className="text-right p-3 font-arabic font-semibold border-b">التاريخ</th>
                    <th className="text-right p-3 font-arabic font-semibold border-b">الخدمة</th>
                    <th className="text-right p-3 font-arabic font-semibold border-b">المبلغ</th>
                    <th className="text-right p-3 font-arabic font-semibold border-b">الحالة</th>
                  </tr>
                </thead>
                <tbody>
                  {mockData.invoices.map((invoice, index) => (
                    <tr key={index} className="border-b border-border/30">
                      <td className="p-3 font-mono text-xs">{invoice.number}</td>
                      <td className="p-3 font-arabic">{new Date(invoice.date).toLocaleDateString('ar-SA')}</td>
                      <td className="p-3 font-arabic">{invoice.service}</td>
                      <td className="p-3 font-mono">{invoice.total.toLocaleString()} ر.س</td>
                      <td className="p-3">
                        <Badge className={`text-xs ${getStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t-2 border-primary pt-6 mt-8">
            <div className="grid grid-cols-2 gap-8 text-sm">
              <div>
                <h4 className="font-arabic font-semibold mb-2">معلومات التواصل</h4>
                <div className="space-y-1 text-muted-foreground">
                  <div>الهاتف: +966 50 123 4567</div>
                  <div>البريد الإلكتروني: <EMAIL></div>
                  <div>الموقع: www.carservice.sa</div>
                </div>
              </div>
              <div className="text-left">
                <h4 className="font-arabic font-semibold mb-2">ملاحظات إضافية</h4>
                <div className="text-xs text-muted-foreground">
                  <div>تم إنشاء هذا التقرير تلقائياً</div>
                  <div>لأي استفسارات، يرجى التواصل معنا</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button onClick={handleDownload} className="flex-1 btn-premium text-white font-arabic gap-2">
            <Download className="w-4 h-4" />
            تحميل PDF
          </Button>
          <Button variant="outline" className="font-arabic gap-2">
            <FileText className="w-4 h-4" />
            طباعة
          </Button>
          <Button variant="outline" onClick={() => setIsOpen(false)} className="font-arabic">
            إغلاق
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}